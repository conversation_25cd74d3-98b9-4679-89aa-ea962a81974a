{"common": {"error": "Error", "success": "Success", "yes": "Yes", "no": "No", "change": "Change", "add": "Add", "close": "Close", "save": "Save", "paginationItemsPerPage": "Show", "paginationTotal": "Showing {{startIndex}} - {{endIndex}} of {{total}} items", "poweredBy": "Powered by", "search": "Search", "loading": "Loading", "searchProducts": "Search Products", "checkout": "Checkout", "delete": "Delete", "back": "Back", "connect": "Connect", "featureInDevelopment": "Feature in development", "n/a": "N/A", "logout": "Logout", "clear": "Clear", "cancel": "Cancel", "remove": "Remove", "reset": "Reset", "edit": "Edit", "invite": "Invite", "select": "Select", "user": "User", "subtotal": "Subtotal", "taxes": "Taxes", "shipping": "Shipping", "free": "Free", "apply": "Apply", "userRoles": {"ADMINISTRATOR": "Administrator"}, "reload": "Reload", "clinicStatus": {"ACTIVE": "Active", "PROCESSING": "Processing", "INACTIVE": "Inactive"}, "clinicAccountType": {"SINGLE": "Single", "GROUP": "Group"}, "comingSoon": "Coming soon!", "download": "Download", "continue": "Continue", "done": "Done", "telNumber": "************"}, "form": {"field": {"field": "Field", "email": "Email", "password": "Password", "name": "Clinic name", "clinicName": "Clinic name", "confirmPassword": "Confirm password", "firstName": "First Name", "lastName": "Last Name", "clinicRole": "Role at Your Clinic", "userType": "User Type", "role": "Role", "phoneNumber": "Phone Number", "title": "Title", "weeklyCogs": "Weekly COGS", "monthlyCogs": "Monthly COGS", "weeklyGA": "Weekly G&A", "monthlyGA": "Monthly G&A", "cogsTarget": "COGS Target", "gaTarget": "G&A Target", "avgLast2WeeksSales": "Awg. 2 wks of sales", "customerNumber": "Customer Number", "street": "Street Address", "state": "State", "city": "City", "postalCode": "Postal Code"}, "errorMessage": {"required": "{{path}} is required", "email": "{{path}} must be a valid email", "min": "{{path}} must be at least {{min}} characters", "max": "{{path}} must be at most {{max}} characters", "matchPassword": "Passwords do not match", "correctTime": "Please choose valid operating hours", "calculateSum": "The sum must be equal to 100", "arrayValue": "At least one field must be filled", "correctFormat": "Field should have correct format({{format}})", "positive": "{{path}} must be positive", "integer": "{{path}} must be an integer", "taxIdFormat": "{{path}} must be in the format xx-xxxxxxx"}}, "apiErrors": {"general": "Something went wrong", "generalHint": "It looks like something went wrong on our end. Please try refreshing the page", "notFound": "Information not found", "REQUIRED": "{{field}} is required", "INVALID_TOKEN": "Invalid token", "INVALID_PASSWORD": "Invalid password", "INVALID_USER": "User doesn't exist", "INVALID_CREDENTIALS": "Invalid email or password", "INVALID_EMAIL": "Invalid email", "ACCOUNT_EXISTS": "Business Tax ID already exists", "CLINIC_EXISTS": "Clinic already exists", "CLINIC_ALREADY_EXISTS": "Clinic already exists", "VENDOR_EXISTS": "Vendor already exists", "USER_EXISTS": "User already exists", "VALIDATION_ERROR": "one or more fields have not been validated"}, "login": {"title": "Welcome Back", "subtitle": "Please enter your credentials and log in.", "register": "Register now!", "rememberMe": "Remember me", "forgotPassword": "Forgot password?", "signIn": "Sign In", "haveNotAccount": "Don’t have an account?", "createAccount": "Create one"}, "register": {"title": "Create your account", "subtitle": "Please enter your email and create a password.", "signUp": "Sign Up", "haveAccount": "Already have an account?", "logIn": "Log in", "userCreated": "User was created."}, "forgotPassword": {"title": "Reset Password", "subtitle": "Enter your email and reset your password.", "resetMessage": "We sent an email with link to change password", "submit": "Submit", "haveAccount": "Already have an account?", "logIn": "Log in"}, "resetPassword": {"title": "New Password", "subtitle": "Set a new password for your account </br> <b>{{ email }}</b>", "confirm": "Confirm", "passwordChanged": "Your password was changed", "wrongTokenLink": "This link has invalid data, please reset your password again", "resetPassword": "Reset password"}, "invitedUser": {"title": "Create your password", "subtitle": "<bold>{{ name }}</bold> invited you to join Highfive.", "signUp": "Sign Up"}, "onboarding": {"shell": {"welcome": "Welcome to Highfive!", "createAccountText": "Complete your Highfive profile in 5 minutes to enhance your experience.", "prev": "Prev step", "next": "Save and Continue", "finish": "Finish"}, "step1": {"clinicDetails": "Clinic Details", "clinicName": "Clinic Name", "ein": "Business Tax ID (EIN)", "personalDetails": "Personal Details", "firstName": "First Name", "lastName": "Last Name", "phoneNumber": "Phone Number", "role": "Role at Your Clinic", "title": "Title", "accountName": "Account Name", "corporateInfo": "Account information", "clinicInfo": "Clinic information", "street": "Street Address", "state": "State", "city": "City", "postalCode": "Postal Code", "primaryDistributor": "Primary Distributor", "selectOne": "Select One", "bannerTitle": "Let's get acquainted!", "bannerDesc": "For all groups with 5 or more clinics will help complete set up for each individual practice under your umbrella."}, "step2": {"signUp": "Sign Up with All Vendors with One Application", "oneApplication": "One Application. Endless Savings Opportunities.", "applicationText": "Unlock a world of savings and convenience with our common application to sign up for all vendors with one-click.", "oneClickSignUp": "1-<PERSON>lick Sign-up", "allReadyHaveAccount": "Already have an account?", "notInterested": "Not interested", "DISTRIBUTOR": "1.Distributors", "MANUFACTURER": "2.Manufacturers (Direct Order)", "COMPOUNDING_PHARMACY": "3.Compounding Pharmacies", "DIAGNOSTICS": "4.Diagnos<PERSON>", "allNotInteresting": "You should select at least one vendor", "bannerTitle": "Tell us a little more about yourselves", "bannerSubtitle": "Personalize Your Purchasing Experience", "bannerDesc": "<PERSON><PERSON><PERSON> wants to know more about your clinic so we can make your experience a valuable one:", "motivatedBy": "Our Clinic is Motivated by <hint>(select one)</hint>", "bestValue": "Best Value (lowest price)", "bestMedicine": "Best Medicine", "bestTechnology": "Best Technology", "priorities": "My Highest Priorities Are <hint>(select all that apply)</hint>", "inventoryCosts": "Inventory Costs", "staffEducation": "Staff Education", "staffManagement": "Staff Management", "increasingHospitalProfit": "Increasing Hospital Profit", "businessGrowth": "Business Growth", "updatingHospitalTech": "Updating Hospital Tech"}, "step3": {"commonApplication": "One Common Application for All Vendors", "automatedRegistration": "Automated registration & license update - no more hassle of individual enrollment", "oneClickSignUp": "1-<PERSON>lick Sign-up", "maximizeSavings": "Maximize your saving opportunities", "maximizeProduct": "Maximize product choices for backorder items", "fillItLatter": "I Will Fill it Out Later", "clinicInformation": "Clinic Information", "clinicNameDBA": "Clinic Name & DBA", "owner": "Owner", "dateEstablished": "Date Established", "primaryContact": "Primary Contact Name", "primaryEmail": "Primary Email", "primaryPhoneNumber": "Primary Phone Number", "billingAddress": "Billing Address", "streetAddress": "Street Address", "stateAddress": "State", "cityAddress": "City", "postalCode": "Postal Code", "sameAddress": "Same as <PERSON><PERSON> Address", "shippingAddress": "Shipping Address", "businessType": "Business Type", "primaryVeterinarianName": "Primary Veterinarian Name", "deaLicenseNumber": "DEA License Number", "deaLicenseExpiration": "DEA License Expiration", "vetLicenseNumber": "Veterinarian License Number", "vetLicenseExpiration": "Veterinarian License Expiration", "attachments": "Attachments", "dragDrop": " Drag & Drop or browse a file", "stateVetLicence": "State Vet License", "deaCertificate": "DEA Certificate", "stateResell": "State Resale/Exemption Form", "salesTax": "Sales Tax Exempt Request Form", "typeOfPractice": "Type of Practice", "clinicPratice": "Clinic/Practice Type", "numberOfVets": "Number of Veterinarians", "avgOfPatientsPerDay": "Avg. # of patients per day", "typeOfProducts": "Type of products you intend to purchase", "avgOfAninalsPerDay": "Avg. # of animals examined / treated / month", "avgOfAninalsEuthanizedPerDay": "Avg. # of animals euthanized / month", "avgOfSurgeryPerMonth": "Avg. # of Surgeries/Month", "avgOfControlledPerMonth": "Avg. # of animals that controlled substances are administered to each month", "clinicOperatingHours": "Clinic Operation Days/Hours", "monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday", "clientsPayment": "Please provide ratio of methods of payment made by clients", "cash": "Cash", "other": "Other", "creditCard": "Credit", "inStateOutState": "Please provide a ratio of in-state to out-of-state patients seen", "inState": "In-State", "outState": "Out-of-State", "bannerTitle": "Upload your clinics information", "bannerDesc": "For all groups with 5 or more clinics will help complete set up for each individual practice under your umbrella.", "listClinic": "LIST OF CLINICS", "addNewClinic": "Add New Clinic", "editNewClinic": "Edit New Clinic", "addedClinics": "Added clinics: {{ number }}", "emptyClinicText": "There are no clinics. Please add clinic.", "addMultiClinics": "ADD MULTIPLE CLINICS", "multiClinicsDesc": "Suitable for more than 5 general practices", "multiClinicsText": "Highfive will add each clinic to your profile . Once completed you will be able to see your entire clinic portfolio under your Clinics List.", "downloadTemplate": "Download .CSV Template", "placeholderFile": "Upload your .CSV file here", "helpText": "If you need assistance with adding a large number of clinics, please contact us:", "modal": {"desc": "Use this forms to add details about your new clinic.", "invite": "Invite manager", "addDetails": "Add clinic details", "ftdvm": "# of FTDVM", "addClinic": "Add clinic", "updateClinic": "Update clinic", "sendInvitation": "Send invitation", "updateInvitation": "Update invitation"}, "emptyClinicError": "Please add at least one clinic"}, "step4": {"deaControlledTitle": "Controlled Drugs", "skipButton": "Skip Controlled Substances", "controlledSubstances": "Controlled Substance Purchases:(Total to equal 100%)", "labelControlledSubstances": "Controlled Substances", "labelNonControlledPrescription": "Non-Controlled Prescriptions", "labelNonPrescription": "Non-Prescriptions(OTC)", "orderingPattern": "Ordering Pattern for Controlled Substances", "otherSuppliers": "Other suppliers of controlled substances", "higestVolume": "List top 5 highest volume, controlled substances of anticipated purchases/actual usage. Start-up entities please provide estimates", "tableHeadTitle1": "Controlled Substance Product", "tableHeadTitle2": "Monthly Usage in Dosage Unit Format", "controlledIntendToPurchase": "Please complete the following table regarding the controlled substances you intend to order", "product": "Product Name", "strength": "Strength & Form", "quantity": "Quantity", "frequency": "Frequency", "labelRegistred": "Are you registered with CSOS?", "lastInspection": "Last inspection date by the DEA", "administerMedications": "Does the facility administer/dispense medications to patients on site?", "takeBackControlled": "Do you take back controlled substances that already have been dispensed to patients?", "ifYes": "If yes, please explain in detail", "federaLicenses": "Are all applicable state and federal licenses current, and issued, for the registered address at which the practitioner is practicing?", "registrantOfficers": "To your knowledge is/has the registrant(s),officer(s), or licensed employee(s)", "underInvestigation": "Currently under investigation by any State authority (Attorney, General’s Office, licensing authority, etc.) or Federal Authority (DEA, FDA, US Attorney’s Office, etc.)?", "crime": "Ever been convicted of a crime related to the distribution of controlled substances or listed chemicals?", "deniedLicence": "Had a license or registration denied, revoked, or suspended by any licensing authority, including DEA, or been the subject of administrative or civil action by any such authority (consent agreement, memorandum of agreement, memorandum of understanding, order to show cause, or immediate suspension order)?", "legalName": "Legal name and title of person who oversees the Controlled Substance Inventory?", "cutOff": "Have you ever been cut-off from purchasing controlled substances from any supplier?", "ifNo": "If no, please explain in detail", "logAll": "Does the registrant maintain a log of all controlled substances administered and controlled substances wasted?", "controlledPolicies": "Does the facility have policies and procedures in place for security and handling of controlled substances?", "trainedInPolicies": "Are all employees that handle controlled substances trained in these policies and procedures?", "deaOperateBussines": "Does the DEA registrant(s) operate any other businesses that require a DEA permit other than what has already been provided?", "congratulations": "Congratulations", "title": "You are a Highfive VIP!", "textLineOne": "Thank you for taking the time to set up your group with Highfive. ", "textLineTwo": "We thank you for your partnership and look forward to working together to make veterinary purchasing joyful!", "price": "It is free to start for independent clinics - no credit card required."}, "step5": {"title": "Credit Application", "creditRequest": "Credit Request", "estimatedMonthlyPurchases": "Est. Monthly Purchases", "paymentOptions": "Payment Option", "preferredPaymentMethod": "Selected Preferred Payment Method", "statementsVia": "Send My Statements Via", "invoicesVia": "Send My Invoices Via", "buyingGroup": "Are you a member of Buying Group", "buyingGroupName": "Buying Group Name", "bankReference": "Bank References", "bankName": "Bank Name", "banker": "Banker", "accountType": "Account Type", "accountNumber": "Account Number", "tradeReferences": "Trade References", "tradeReferencesName": "Name", "address": "Address", "accountNumberReference": "Account Number", "phoneNumber": "Phone Number", "acceptTerms": "I accept <linkTerms>the Terms of Use</linkTerms> and <linkPrivacy>Privacy Policy</linkPrivacy>", "signature": "Signature", "openBilling": "Open Billing", "autoCharge": "Auto Charge", "purchaseOrder": "Purchase Order", "lessThan5": "$5,000 or less", "5to25": "$5,000 to $25,000", "25to75": "$25,000 to $75,000", "over75": "Over $75,000 (Please indicate amount)", "oneTime": "One Time Use Only", "autoChargeOnDate": "Auto Charge on Due Date", "prepay": "Prepay", "creditCard": "Credit Card", "ach": "ACH Draft (Please attach a copy of voided check to completed form)", "email": "Email", "fax": "Fax", "online": "I will obtain online", "checking": "Checking", "savings": "Savings", "billing": "Billing", "shipping": "Shipping", "emptyReferences": "Don't worry, you can leave this blank if you don't have references."}, "comingSoon": {"title": "Coming Soon in August!", "subtitle": "Highfive Vet | The Joy of Veterinary Purchasing", "textOne": "Our mission is to make veterinary purchasing, ", "easy": "easy & joyful", "textTwo": "one cart at a time, through our ", "automation": "powerful automation platform.", "textThreee": "We make it easy to", "spendLess": "save time & spend less.", "textFour": "We’re so happy that you’re joining this journey with us & here’s a sneak peek of what’s to come!", "newsletter": "Share with your friends & get 1 month free of Highfive Pro Plan!", "email": "Email", "share": "Share"}, "amazonBusiness": {"title": "Highfive Vet & Amazon Business", "subtitle": "Purchasing Platform for Veterinary Clinics", "tagline": "Turning veterinary purchasing from mundane to joyful with automated, data-driven solutions that maximize savings and simplify business operations.", "connect": "Do you have an existing Highfive Account?", "existingAccount": "Existing Customers: Log in to Connect", "newAccount": "New Customers: Book a Demo"}, "done": {"congrats": "Congratulations", "title": "You are a Highfive VIP!", "text": "Thank you for taking the time to set up your group with <b>Highfive</b>. <br /> We <b>thank you</b> for your partnership and look forward to <b>working together</b> to make veterinary purchasing joyful!", "priceTitle": "Current multi clinic pricing is:", "price": "$35 / clinic per month", "paymentTitle": "Enter your payment details below:", "subscribe": "Subscribe", "complete": "Congratulations! Click to complete."}}, "sidebar": {"logout": "Logout", "home": "Home", "shoppingList": "Shopping List", "cart": "<PERSON><PERSON>", "inventory": "Inventory", "orderHistory": "Order History", "vendors": "Vend<PERSON>", "invoices": "Invoices", "settings": "Settings", "accountSettings": "Account <PERSON><PERSON>", "dashboard": "Dashboard", "clinicManagement": "Clinic Management", "backTo": "Back to {{page}}", "blockMessage": "Please connect min. 1 vendor account to access our platform"}, "client": {"common": {"addToCart": "Add to Cart", "change": "Change quantity", "account": "Account", "clinic": "Clinic", "inStock": "In Stock", "outOfStock": "Out of Stock", "dropShip": "Drop Ship", "specialOrder": "Special Order", "backorder": "Backorder", "emptyTable": "Nothing was found"}, "cart": {"headerTitle": "<PERSON><PERSON>", "emptyCartTitle": "Your Highfive cart is empty!", "emptyCartDescription": "Search for items and add them to you cart", "emptyCartDescription1": "You can ", "emptyCartDescription2": "search for products", "emptyCartDescription3": " to explore and find items to add to your cart.", "emptyCartSmartCart": "If you're unsure what to include, use the button below to auto-fill your cart with recommended items.", "totalVendors": "{{count}} vendors", "shippingFee": "Shipping Fee ({{fee}})", "removeModalTitle": "Remove Item", "subtotalSingleItem": "Subtotal (1 item)", "subtotalPluralItems": "Subtotal ({{count}} items)", "editNote": "To edit a note, click on the icon", "swapVendor": "<PERSON><PERSON><PERSON>", "swapVendorWithNumber": "<PERSON>wap <PERSON> ({{number}})", "removeModalText": "Are you sure you want to delete <strong>{{itemName}}?</strong>", "clearCart": "Clear Cart", "clearCartDescription": "Are you sure you want to delete all the items inside your cart?", "incrementsOf": "Increments of {{value}}", "budgetTitle": "Spend vs. Budget", "setBudget": "Set Budget", "budgetText": "Showing historical purchase + current cart", "budgetEmptyText": "No budget has been set yet", "budgetTarget": "target", "budgetUsed": " used", "weekly": "Weekly", "monthly": "Monthly", "COGS": "COGS", "GA": "GA", "lastOrder": "Last order: <span>{{quantity}} on {{date}}</span>", "generateSmartCart": "Generate Smart Cart"}, "checkout": {"headerTitle": "Checkout", "shippingAddress": "Shipping Address", "billingAddress": "Billing Address", "paymentMethod": "Payment Method", "shippingTimeOption": "Shipping Time Option", "isBillingAddressSame": "Same as Shipping Address", "paymentMethodPlaceholder": "Select a Payment Method", "shippingTimePlaceholder": "Select a Shipping Time", "placeOrder": "Place Order", "editBillingAddress": "Edit Billing Address", "street": "Street Address", "city": "City", "state": "State", "postalCode": "Postal Code", "noAddress": "No address specified", "orderConfirmed": "Order Confirmed", "highfivePO": "Highfive PO #:", "orderConfirmedDescription": "We have sent you a confirmation Email. You can review your order in the “Order History” screen"}, "search": {"title": "Search", "showingResults": "{{totalPageStart}}-{{totalPageEnd}} from {{total}} result(s) for \"<searchValue>{{searchValue}}</searchValue>\"", "addToCart": "Add To Cart", "products": {"headers": {"product": "Name of product", "brand": "MFR", "vendor": "<PERSON><PERSON><PERSON>", "lastOrdered": "Last Ordered", "price": "Price/List Price", "qty": "Qty.", "inCart": "In Cart"}}, "emptySearch": "No results for", "emptySearchQuery": "Use the search field to search for products", "applyFilter": "Apply Filter", "resetFilter": "Clear Filters", "filter": "Filter"}, "productItem": {"title": "Product Detail Page", "sku": "SKU", "type": "Type", "packType": "Pack Type", "brand": "MFR", "lastOrders": "Last Orders", "lastOrder": "{{number}} on {{date}}", "totalSpent": "<span>{{total}}</span> total spent", "ordersMade": "<span>{{ordersMade}}</span> orders made", "averageQty": "<span>{{averageQty}}</span> average qty."}, "orderHistory": {"title": "Order History", "dateFilter": "Date Filter", "dateFilterFrom": "from {{date}}", "dateFilterTo": "to {{date}}", "from": "From", "to": "To", "invalidDate": "End date cannot be before start date", "lastWeek": "Last Week", "6months": "Last 6 Months", "year": "Last 12 Months", "search": "Search", "showResult": "Showing results ", "clearFilter": "Clear Filter", "searchOrder": "Search Order", "headers": {"date": "Date", "PO": "Highfive PO #", "vendors": "# of Vendors", "items": "Total Items", "orderTotal": "Order Total", "status": "Status"}, "emptyTable": "You have no orders", "ACCEPTED": "Processing", "PROCESSING": "Processing", "PARTIALLY_SHIPPED": "Partially shipped", "SHIPPED": "Shipped", "PARTIALLY_DELIVERED": "Partially delivered", "NON_TRACKABLE": "Non-trackable item", "DELIVERED": "Delivered", "CANCELLED": "Cancelled", "BACKORDERED": "Backordered", "RETURNED": "Returned", "REJECTED": "Failed", "PLACEMENT_FAILED": "Pending", "PENDING": "Pending"}, "orderHistoryItem": {"emptyTable": "Nothing was found", "filterProducts": "Filter products", "notAvailable": "This product is no longer available.", "tableTitle": "Order items", "title": "Order Details", "headers": {"date": "Date", "PO": "Highfive PO #", "vendors": "Vend<PERSON>", "items": "Items", "subtotal": "Subtotal", "taxes": "Taxes", "shipping": "Shipping", "orderTotal": "Order Total", "title": "Product", "sku": "SKU", "vendor": "<PERSON><PERSON><PERSON>", "brand": "MFR", "qty": "Qty.", "price": "Price", "netTotal": "Net Total", "order": "Order #", "status": "Status"}}, "inventory": {"headerTitle": "Inventory"}, "vendors": {"headerTitle": "Vend<PERSON>", "filterVendors": "<PERSON><PERSON>", "hasAccount": "Existing Accounts", "optIn": "1-<PERSON><PERSON> Sign-Up", "optOut": "Not Interested", "username": "Username", "customerNumber": "Customer Number", "password": "Password", "appPendingTooltip": "Application Pending", "statuses": {"notConnected": "Not Connected", "syncing": "Connecting", "connected": "Connected", "appPending": "App. Pending"}, "amazon": {"connectAccount": "Connect Your Account", "connectAccountToBusiness": "Connect Highfive in Amazon Business", "convertAccount": "Convert Amazon Personal Account", "connectWithVendor": "Connect with your vendor", "discount": "50% Prime Discount for the first year.", "discountApplied": "I applied the discount code.", "existingAccount": "Existing Amazon Business Account", "loginToAmazon": "Log Into Your Amazon Business Account", "needHelp": "Need help?", "nextStep": "Next Step", "noAccount": "No Amazon Business Account", "pasteCode": "Paste this code on your connection option below:", "previousStep": "Previous Step", "specialOfferDescription": "Highfive & Amazon Business have a special offer of <highlight>50% Amazon Business Prime</highlight> AND special discounted product pricing! Follow the steps below to redeem and connect.", "watchTutorial": "Watch our Tutorial", "watchedTutorial": "I watched the connection tutorial", "watchConnectionTutorial": "Watch Connection Tutorial", "yourCode": "Your code:"}}, "settings": {"title": "Settings", "clientDetails": "Clinic Details", "budget": {"title": "Budget", "budgetType": "Budget Settings", "lastUpdated": "last updated {{ date }}", "staticType": "Static Budget (ex. $5000)", "weeklyCOGS": "Weekly COGS", "monthlyCOGS": "Monthly COGS", "weeklyGA": "Weekly G&A", "monthlyGA": "Monthly G&A", "dynamicType": "Dynamic Budget (based on target %)", "COGSTarget": "COGS Target", "GATarget": "G&A Target", "avgTwoWeeksSales": "Last Weeks Revenue", "monthToDateSales": "Month to Date Revenue", "threshold": "<PERSON><PERSON><PERSON><PERSON>", "externalWeeklyCogs": "External Weekly COGS", "externalMonthlyCogs": "External Monthly COGS", "budgetPreferences": "External Data", "costsInCOGS": "Include external COGS", "COGSHelp": "COGS (cost of goods sold) means all costs of goods directly associated with generating revenue (ex. pharmacy, lab)", "GAHelp": "G&A means all indirect expenses related to the back office but not directly related to revenue (ex. printer ink, paper)"}, "controlledDrugs": "Controlled Drugs", "personalDetails": "Personal Details", "preferences": "Preferences", "updatedSettings": "{{tab}} was updated", "resetPassword": "Reset Password", "resetPasswordText": "An email will be sent to: {{ email }}", "users": {"tabName": "Users", "inviteUser": "Invite User", "addExistingUser": "Add Existing User", "existingUser": "Existing Users", "user": "User", "addedUserSuccess": "User was added", "inviteUserSuccess": "User was invited", "editUser": "Edit User", "editUserSuccess": "User was updated", "removeModalTitle": "Remove User", "removeModalText": "Are you sure you want to remove", "accountFeatures": {"columnName": "User Accounts Features", "manageAccount": "Manage Account", "manageClinic": "Manage Clinic", "manageUsers": "Manage Users", "placeOrders": "Place Orders", "modifyCart": "Modify Cart", "viewOrderHistory": "View Order History"}}, "externalData": {"title": "External Data", "revenue": "Revenue", "revenueDesc": "Revenue means sales generated by your clinic, and we use this data to provide spend metrics & dashboard.", "lastSales": "Awg. last 2 wks of sales", "monthDate": "Month-to-date", "revenueInfo": "This panel gets updated every Sunday for clinics without PIMS integration <br /> last updated: {{date}}"}}, "page404": {"title": "Oops! Page Not Found", "subtitle": "It looks like the page you are looking for doesn't exist or has been moved. But don't worry, we're here to help you find what you need.", "toHome": "Go to home", "goBack": "Go Back"}, "clinicList": {"title": "Clinics List", "search": "Search", "createCohort": "Create Cohort", "columns": {"clinicName": "Clinic Name", "state": "State", "ftdvm": "#FTDVM", "revenue": "Rev.", "actualSpend": "Actual <br /> Spend", "budget": "Budget", "budgetActual": "Budget to <br /> Actual %", "costRevenue": "Cost to <br /> Revenue", "orders": "# of <br /> Orders", "vendors": "# of <br /> Vendors"}}, "dashboard": {"title": "Dashboard", "overview": "Overview", "charts": "Charts", "tops": "Tops", "revenue": "Revenue", "cogs": "COGS", "ga": "G&A", "actual": "Budget to Actual %", "cogsRevenue": "COGS to revenue %", "gaRevenue": "G&A to revenue %", "yoy": "YoY %: --", "corporateRevenue": "Corporate Revenue", "corporateCogsRevenue": "Corporate COGS to Revenue", "budgetCOGSRevenue": "Budget, COGS & Revenue", "yearOverYear": "year-over-year %", "viewAll": "View All", "cardPointTitle": "Top 5 Clinics Budget to Actual %", "startDate": "Start Date", "endDate": "End Date", "download": "Download", "vendors": "Vend<PERSON>", "columns": {"vendor": "<PERSON><PERSON><PERSON>", "spend": "$ Spend", "orders": "# Orders", "allOrders": "% All vendor Orders", "reqSpend": "Contract Req $Spend", "rank": "Rank"}, "timePeriod": {"monthDate": "Month to Date", "lastMonth": "Last Month", "last3Month": "Last 3 Month", "last6Month": "Last 6 Month", "yearDate": "Year to Date", "placeholder": "Select period", "label": "Select period"}, "createCohort": "Create Cohort", "comingSoon": "Coming soon... Follow us!", "startEarning": "Start Earning {{currentRebatePercent}}% Back", "unlockRebate": "Shop now to start unlock your rebate", "shopNow": "Shop Now"}, "clinicManagement": {"title": "", "columns": {"name": "Clinic Name", "manager": "Manager", "email": "Email", "status": "Status"}, "connectVendor": "Connect vendor", "addClinic": "Add Clinic"}, "invoices": {"title": "Invoices", "description": "Manage your payables and invoices", "loading": "Loading invoices...", "load": "Load Invoices", "noClinic": {"title": "No Clinic Selected", "message": "Please select a clinic to view invoices."}, "error": {"title": "Error Loading Invoices", "message": "Failed to load invoices. Please try again.", "retry": "Retry"}, "onboarding": {"title": "Complete Your Payment Setup", "description": "Before you can manage invoices and payables, please complete the onboarding process to set up your payment information and verify your business details.", "status": "Status"}}}, "superAdmin": {"dashboard": {"title": "Clinics List", "addNewClinic": "Add New Clinic", "columns": {"name": "Clinic name", "location": "Location", "status": "Status", "type": "Type"}, "deleteClinicTitle": "Delete clinic", "deleteDesc": "Are you sure you want to delete <span>{{ clinicName }}?<span>"}}}