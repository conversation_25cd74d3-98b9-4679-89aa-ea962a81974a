import { useMemo } from 'react';
import { ProductType } from '@/types';

type ListType = {
  createdBy: string;
  lastUpdated: string;
  name: string;
  products: ProductType[];
};

type UseListFilteringProps = {
  lists: ListType[];
  searchQuery: string;
};

type FilteredList = {
  isVisible: boolean;
  sortedProducts: ProductType[];
} & ListType;

type UseListFilteringReturn = {
  filteredLists: FilteredList[];
};

export const useListFiltering = ({
  lists,
  searchQuery,
}: UseListFilteringProps): UseListFilteringReturn => {
  const filteredLists = useMemo(() => {
    if (!searchQuery) {
      return lists.map((list) => ({
        ...list,
        isVisible: true,
        sortedProducts: list.products,
      }));
    }

    return lists.map((list) => {
      const listNameMatches = list.name.toLowerCase().includes(searchQuery);

      if (listNameMatches) {
        return {
          ...list,
          isVisible: true,
          sortedProducts: list.products,
        };
      }

      const matchingProducts: ProductType[] = [];
      const nonMatchingProducts: ProductType[] = [];

      list.products.forEach((product) => {
        const productNameMatches = product.name
          .toLowerCase()
          .includes(searchQuery);
        if (productNameMatches) {
          matchingProducts.push(product);
        } else {
          nonMatchingProducts.push(product);
        }
      });

      const hasMatchingProducts = matchingProducts.length > 0;

      return {
        ...list,
        isVisible: hasMatchingProducts,
        sortedProducts: hasMatchingProducts
          ? [...matchingProducts, ...nonMatchingProducts]
          : list.products,
      };
    });
  }, [lists, searchQuery]);

  return {
    filteredLists,
  };
};
