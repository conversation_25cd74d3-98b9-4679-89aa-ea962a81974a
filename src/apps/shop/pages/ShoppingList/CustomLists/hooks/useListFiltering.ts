import { useMemo } from 'react';
import { ProductType } from '@/types';

interface ListType {
  name: string;
  products: ProductType[];
}

interface UseListFilteringProps {
  lists: ListType[];
  searchQuery: string;
}

interface FilteredList extends ListType {
  isVisible: boolean;
  sortedProducts: ProductType[];
  isProductMatch: (product: ProductType) => boolean;
}

interface UseListFilteringReturn {
  filteredLists: FilteredList[];
}

export const useListFiltering = ({
  lists,
  searchQuery,
}: UseListFilteringProps): UseListFilteringReturn => {
  const filteredLists = useMemo(() => {
    if (!searchQuery.trim()) {
      return lists.map((list) => ({
        ...list,
        isVisible: true,
        sortedProducts: list.products,
        isProductMatch: () => false,
      }));
    }

    const query = searchQuery.toLowerCase();

    return lists.map((list) => {
      const listNameMatches = list.name.toLowerCase().includes(query);

      if (listNameMatches) {
        return {
          ...list,
          isVisible: true,
          sortedProducts: list.products,
          isProductMatch: () => false,
        };
      }

      const matchingProducts: ProductType[] = [];
      const nonMatchingProducts: ProductType[] = [];

      list.products.forEach((product) => {
        const productNameMatches = product.name.toLowerCase().includes(query);
        if (productNameMatches) {
          matchingProducts.push(product);
        } else {
          nonMatchingProducts.push(product);
        }
      });

      const hasMatchingProducts = matchingProducts.length > 0;

      return {
        ...list,
        isVisible: hasMatchingProducts,
        sortedProducts: hasMatchingProducts
          ? [...matchingProducts, ...nonMatchingProducts]
          : list.products,
        isProductMatch: (product: ProductType): boolean => {
          return product.name.toLowerCase().includes(query);
        },
      };
    });
  }, [lists, searchQuery]);

  return {
    filteredLists,
  };
};
