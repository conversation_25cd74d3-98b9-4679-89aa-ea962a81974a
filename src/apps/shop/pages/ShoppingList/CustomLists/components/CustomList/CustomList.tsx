import { CollapsiblePanel } from '@/libs/ui/CollapsiblePanel/CollapsiblePanel';
import { Checkbox } from '@/libs/form/Checkbox';
import { Button } from '@/libs/ui/Button/Button';
import { EmptyState } from './components/EmptyState/EmptyState';
import { ProductType } from '@/types';
import { SHOP_ROUTES_PATH } from '@/apps/shop/routes/routes';
import { Item } from './components/Item/Item';

type Props = {
  setActiveTab: (tab: number) => void;
  list: {
    name: string;
    products: ProductType[];
  };
  isProductMatch?: (product: ProductType) => boolean;
};

export const CustomList = ({ setActiveTab, list, isProductMatch }: Props) => {
  const sortedProducts = list.products;
  const productMatchFn = isProductMatch || (() => false);

  return (
    <CollapsiblePanel
      variant="white"
      header={
        <div className="flex h-[72px] w-full items-center justify-between bg-white p-5 pr-16 pb-0">
          <Checkbox checked={false} onChange={() => {}} />
          <div className="ml-4 flex w-full flex-col gap-1">
            <span className="text-sm font-semibold">{list.name}</span>
            <div className="flex">
              <span className="text-xs text-black/50">
                Total of Items:{' '}
                <span className="font-semibold text-black/70">
                  {sortedProducts.length}
                </span>
              </span>
              <div className="divider-v"></div>
              <span className="text-xs text-black/50">
                Created by:{' '}
                <span className="font-semibold text-black/70">Lima Neto</span>
              </span>
              <div className="divider-v"></div>
              <span className="text-xs text-black/50">
                Last Updated:{' '}
                <span className="font-semibold text-black/70">04/05/2025</span>
              </span>
            </div>
          </div>
          {sortedProducts && sortedProducts.length > 0 ? (
            <Button className="w-40" size="sm" to={SHOP_ROUTES_PATH.checkout}>
              Checkout List
            </Button>
          ) : (
            <Button className="w-52" size="sm" onClick={() => setActiveTab(1)}>
              Add Products to List
            </Button>
          )}
        </div>
      }
      content={
        <div className="bg-white p-5">
          <div className="flex flex-col gap-1 bg-[#F8FBFD] p-4">
            {sortedProducts && sortedProducts.length > 0 ? (
              sortedProducts.map((product) => (
                <Item
                  key={product.id}
                  product={product}
                  isHighlighted={productMatchFn(product)}
                />
              ))
            ) : (
              <EmptyState setActiveTab={setActiveTab} />
            )}
          </div>
          {!sortedProducts ||
            (sortedProducts.length === 0 && (
              <Button
                onClick={() => setActiveTab(1)}
                variant="unstyled"
                className="mt-4 w-full text-right text-blue-600"
              >
                <p className="mr-4 ml-auto">+ Add more products</p>
              </Button>
            ))}
        </div>
      }
      startOpen
    />
  );
};
