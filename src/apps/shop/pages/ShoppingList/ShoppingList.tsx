import { useState } from 'react';
import { Tabs } from '@/libs/ui/Tabs/Tabs';
import { CustomLists } from './CustomLists/CustomLists';

export const ShoppingList = () => {
  const [activeTab, setActiveTab] = useState(0);

  const tabs = [
    {
      label: 'Your Custom Lists',
      onClick: (index: number) => setActiveTab(index),
    },
    {
      label: 'Previously Purchased',
      onClick: (index: number) => setActiveTab(index),
    },
  ];

  return (
    <div className="m-6 mt-8 flex h-full flex-col items-center rounded-sm border border-black/[0.06] bg-white p-6">
      <h1 className="text-lg font-semibold">Your Shopping Lists</h1>
      <div className="mt-5 w-lg font-medium">
        <Tabs active={activeTab} tabs={tabs} />
      </div>
      <div className="mt-6 w-full rounded-sm bg-gray-100/50 p-4">
        {activeTab === 0 && <CustomLists setActiveTab={setActiveTab} />}
        {activeTab === 1 && (
          <div className="text-center text-gray-600">
            Previously Purchased items
          </div>
        )}
      </div>
    </div>
  );
};
