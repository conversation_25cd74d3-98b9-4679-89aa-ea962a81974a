import { useNavigate } from 'react-router-dom';

import { GPO_ROUTES_PATH } from '@/apps/gpo-portal/routes/routes';
import { ResetPasswordForm } from '@/libs/auth/components/ResetPasswordForm/ResetPasswordForm';
import { fetchApi } from '@/libs/utils/api';
import { successNotification } from '@/utils';
import { useTranslation } from 'react-i18next';

import { SCHEMA } from './constants';

export const ResetPassword = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();

  const resetPasswordApiFunc = async (values?: {
    password: string;
    confirmPassword: string;
  }) => {
    if (!values) return;

    await fetchApi('/gpo/password-resets', {
      method: 'POST',
      body: values,
    });

    successNotification(t('gpo.resetPassword.successMessage'));
  };

  return (
    <ResetPasswordForm
      apiFunc={resetPasswordApiFunc}
      onSuccess={() => navigate(GPO_ROUTES_PATH.login)}
      schema={SCHEMA}
      namespace="gpo.resetPassword"
    />
  );
};
